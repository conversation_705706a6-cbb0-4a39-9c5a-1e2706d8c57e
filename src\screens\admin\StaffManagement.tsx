import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, Dimensions, Platform } from 'react-native';
import {
  Text,
  Button,
  Card,
  TextInput,
  Portal,
  Modal,
  IconButton,
  Searchbar,
  Divider,
  ActivityIndicator,
  Dialog,
  Paragraph,
  Avatar,
  useTheme
} from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import type { CreateStaffData, StaffMember, UpdateStaffData } from '../../types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { searchUsers } from '../../utils/searchUtils';

// Simple responsive breakpoints
const SCREEN_BREAKPOINT_TABLET = 768;

export const StaffManagement = ({ route, navigation }: any) => {
  const { createStaffMember } = useAuth();
  const paperTheme = useTheme();
  const staffType = route.params?.staffType || 'doctor';

  // Get screen width for responsive design
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [staffData, setStaffData] = useState<StaffMember[]>([]);
  const [currentEditingStaff, setCurrentEditingStaff] = useState<StaffMember | null>(null);

  // State for delete confirmation dialog
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [staffToDelete, setStaffToDelete] = useState<string | null>(null);
  const [formData, setFormData] = useState<CreateStaffData | UpdateStaffData>({
    phoneNumber: '',
    designation: '',
    department: '',
    email: '',
    name: '',
    role: staffType,
    password: '',
    specialization: '',
  });
  const [formError, setFormError] = useState('');

  // Add state for password visibility
  const [passwordVisible, setPasswordVisible] = useState(false);

  const fetchStaffData = async () => {
    try {
      setIsLoading(true);
      const endpoint = staffType === 'doctor'
        ? `${API_BASE_URL}/api/staff/doctors`
        : `${API_BASE_URL}/api/staff/counsellors`;

      const response = await fetch(endpoint);
      if (!response.ok) throw new Error('Failed to fetch staff data');
      const data = await response.json();
      console.log(data); // Log the fetched staff data
      setStaffData(data.map((staff: any) => ({
        ...staff,
        id: staff._id,
      })));
    } catch (error) {
      console.error('Error fetching staff:', error);
      setFormError('Failed to load staff data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStaffData();
  }, [staffType]);

  // Add event listener for screen dimension changes
  useEffect(() => {
    const updateDimensions = () => {
      setScreenWidth(Dimensions.get('window').width);
    };

    // Set up event listener for dimension changes
    const dimensionsSubscription = Dimensions.addEventListener('change', updateDimensions);

    // Clean up event listener on component unmount
    return () => {
      dimensionsSubscription.remove();
    };
  }, []);

  const handleAddOrEditStaff = async () => {
    try {
      setFormError('');
      setIsLoading(true);

      if (!formData.email || !formData.name) {
        throw new Error('Please fill in all required fields');
      }

      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        throw new Error('Please enter a valid email address');
      }

      if (!currentEditingStaff && !formData.password) {
        throw new Error('Password is required for new staff members');
      }

      if (!currentEditingStaff && formData.password && formData.password.length < 6) {
        throw new Error('Password must be at least 6 characters');
      }

      if (currentEditingStaff) {
        // UPDATE existing staff
        const updateData: UpdateStaffData = {
          name: formData.name,
          email: formData.email,
          role: formData.role as 'doctor' | 'drug-counsellor',
          specialization: formData.specialization,
          phoneNumber: formData.phoneNumber,
          designation: formData.designation,
          department: formData.department,
        };

        // Only include password if it has been changed
        if (formData.password) {
          updateData.password = formData.password;
        }

        const response = await fetch(`${API_BASE_URL}/api/staff/${currentEditingStaff.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData),
        });

        if (!response.ok) throw new Error('Failed to update staff');

        fetchStaffData();
        Alert.alert('Success', 'Staff updated successfully');
      } else {
        // CREATE new staff
        await createStaffMember(formData as CreateStaffData);
        fetchStaffData();
        Alert.alert('Success', 'Staff created successfully');
      }

      setShowModal(false);
      resetForm();
    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Failed to save staff member');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      phoneNumber: '',
      designation: '',
      department: '',
      email: '',
      name: '',
      role: staffType,
      password: '',
      specialization: '',
    });
    setCurrentEditingStaff(null);
    setPasswordVisible(false); // Reset password visibility
  };

  const handleEditStaff = (staff: StaffMember) => {
    console.log('[EDIT] Editing staff:', staff);
    setFormData({
      phoneNumber: staff.phoneNumber || '',
      designation: staff.designation || '',
      department: staff.department || '',
      email: staff.email || '',
      name: staff.name || '',
      role: staff.role,
      password: '',
      specialization: staff.specialization || '',
    });
    setCurrentEditingStaff(staff);
    setShowModal(true);
  };
  // Function to show the delete confirmation dialog
  const handleDeleteStaff = (id: string) => {
    console.log(`[DELETE INITIATED] Attempting to delete ${staffType === 'doctor' ? 'Doctor' : 'Drug Counsellor'} with ID: ${id}`);
    setStaffToDelete(id);
    setDeleteDialogVisible(true);
  };

  // Function to handle delete confirmation
  const handleDeleteConfirm = () => {
    if (staffToDelete) {
      deleteStaff(staffToDelete);
      setDeleteDialogVisible(false);
      setStaffToDelete(null);
    }
  };

  // Function to dismiss the delete dialog
  const hideDeleteDialog = () => {
    setDeleteDialogVisible(false);
    setStaffToDelete(null);
  };

  const deleteStaff = async (id: string) => {
    console.log('deleteStaff called with ID:', id);  // Ensure this is logged

    try {
      const token = await AsyncStorage.getItem('token');
      console.log('Token:', token);  // Ensure the token is fetched properly

      if (!token) {
        console.log('No token found');
        return;
      }

      const url = `${API_BASE_URL}/api/staff/${id}`;
      console.log('[FRONTEND DELETE] Sending DELETE to:', url);  // Check the URL in console

      // Send DELETE request with axios
      const response = await axios.delete(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      console.log('[FRONTEND DELETE] Response:', response); // Log the response
      if (response.status === 200) {
        Alert.alert('Deleted', 'Staff deleted successfully');
        setStaffData(prev => prev.filter(staff => staff.id !== id));// Refresh staff list after deletion
      } else {
        console.log('[FRONTEND DELETE] Deletion failed:', response.data);
        throw new Error(response.data.error || 'Failed to delete staff');
      }
    } catch (error: any) {
      console.error('[FRONTEND DELETE] Error:', error.message);
      Alert.alert('Error', error.message || 'Delete failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Enhanced search using the search utility function
  const filteredStaff = searchUsers(staffData, searchQuery, ['phoneNumber', 'designation', 'department', 'specialization']);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[
        styles.header,
        screenWidth < SCREEN_BREAKPOINT_TABLET && styles.headerSmallScreen
      ]}>
        <View style={styles.headerLeftSection}>
          <Text style={styles.title}>
            {staffType === 'doctor' ? 'Doctors' : 'Drug Counsellors'} List
          </Text>
        </View>
        <Button
          mode="contained"
          icon="plus"
          onPress={() => { resetForm(); setShowModal(true); }}
          buttonColor={theme.colors.primary}
          labelStyle={screenWidth < 400 ? { fontSize: 12 } : {}}
        >
          {screenWidth < 400 ? "Add" : "Add New"}
        </Button>
      </View>

      {/* Search */}
      <Searchbar
        placeholder="Search ...."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
        iconColor={theme.colors.primary}
      />

      {/* Staff List */}
      <ScrollView style={styles.staffList}>
        <View style={screenWidth >= SCREEN_BREAKPOINT_TABLET ? styles.staffGridContainer : {}}>
          {filteredStaff.map((staff) => (
            <Card
              key={staff.id}
              style={[
                styles.staffCard,
                screenWidth >= SCREEN_BREAKPOINT_TABLET && styles.staffCardGrid
              ]}
            >
              <Card.Content>
                <View style={[
                  styles.staffHeader,
                  screenWidth < 500 && styles.staffHeaderSmallScreen
                ]}>
                  <View style={screenWidth < 500 ? { flex: 1 } : {}}>
                    <Text style={styles.staffName}>{staff.name}</Text>
                    <Text style={styles.staffEmail}>{staff.email}</Text>
                  </View>
                  <View style={styles.actionButtons}>
                    <Button
                      mode="outlined"
                      onPress={() => handleEditStaff(staff)}
                      style={styles.actionButton}
                      textColor={theme.colors.primary}
                      labelStyle={screenWidth < 400 ? { fontSize: 12 } : {}}
                    >
                      Edit
                    </Button>
                    <Button
                      mode="outlined"
                      onPress={() => handleDeleteStaff(staff.id)}
                      style={styles.actionButton}
                      textColor={theme.colors.error}
                      labelStyle={screenWidth < 400 ? { fontSize: 12 } : {}}
                    >
                      Delete
                    </Button>
                  </View>
                </View>
                <Divider style={styles.divider} />
                <View style={styles.staffDetailsContainer}>
                  <View style={styles.staffDetails}>
                    <Text style={styles.detailLabel}>Specialization:</Text>
                    <Text style={styles.detailValue}>{staff.specialization}</Text>
                  </View>
                  <View style={styles.staffDetails}>
                    <Text style={styles.detailLabel}>Department:</Text>
                    <Text style={styles.detailValue}>{staff.department}</Text>
                  </View>
                  <View style={styles.staffDetails}>
                    <Text style={styles.detailLabel}>Designation:</Text>
                    <Text style={styles.detailValue}>{staff.designation}</Text>
                  </View>
                  <View style={styles.staffDetails}>
                    <Text style={styles.detailLabel}>Phone:</Text>
                    <Text style={styles.detailValue}>{staff.phoneNumber}</Text>
                  </View>
                </View>
              </Card.Content>
            </Card>
          ))}
        </View>
      </ScrollView>

      {/* Delete Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={deleteDialogVisible}
          onDismiss={hideDeleteDialog}
          style={[styles.deleteDialogContainer, { width: screenWidth > 500 ? 400 : '85%', alignSelf: 'center' }]}
        >
          <View style={styles.iconContainer}>
            <Avatar.Icon
              size={screenWidth < SCREEN_BREAKPOINT_TABLET ? 48 : 56}
              icon="alert"
              color="white"
              style={{ backgroundColor: paperTheme.colors.error }}
            />
          </View>
          <Dialog.Title style={[
            styles.dialogTitle,
            screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 18, marginBottom: theme.spacing.xs }
          ]}>
            Confirm Deletion
          </Dialog.Title>
          <Dialog.Content style={styles.dialogContent}>
            <Paragraph style={[
              styles.dialogMessage,
              screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 14, marginBottom: theme.spacing.sm }
            ]}>
              Are you sure you want to delete this {staffType === 'doctor' ? 'doctor' : 'drug counsellor'}? This action cannot be undone.
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions style={[
            styles.dialogActions,
            screenWidth < SCREEN_BREAKPOINT_TABLET && {
              paddingHorizontal: theme.spacing.xs,
              paddingBottom: theme.spacing.xs,
              justifyContent: 'space-between'
            }
          ]}>
            <Button
              onPress={hideDeleteDialog}
              style={[
                styles.cancelButton,
                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                  paddingHorizontal: theme.spacing.xs,
                  minWidth: 60
                }
              ]}
              labelStyle={[
                { color: paperTheme.colors.onSurface },
                screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 12 }
              ]}
              mode="outlined"
            >
              Cancel
            </Button>
            <Button
              onPress={handleDeleteConfirm}
              mode="contained"
              style={[
                styles.deleteButton,
                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                  paddingHorizontal: theme.spacing.xs,
                  minWidth: 60
                }
              ]}
              labelStyle={screenWidth < SCREEN_BREAKPOINT_TABLET ? { fontSize: 12, color: 'white' } : { color: 'white' }}
            >
              Delete
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Add/Edit Modal */}
      <Portal>
        <Modal
          visible={showModal}
          onDismiss={() => { 
            setShowModal(false); 
            resetForm(); 
            setPasswordVisible(false); // Reset password visibility
          }}
          contentContainerStyle={[
            styles.modalContainer,
            { width: screenWidth >= SCREEN_BREAKPOINT_TABLET ? '60%' : '85%' }
          ]}
        >
          <Text style={styles.modalTitle}>
            {currentEditingStaff ? 'Edit' : 'Add New'} {staffType === 'doctor' ? 'Doctor' : 'Drug Counsellor'}
          </Text>

          {formError ? <Text style={styles.error}>{formError}</Text> : null}

          <TextInput
            label="Full Name"
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
            style={styles.input}
            mode="outlined"
          />

          <TextInput
            label="Email Address"
            value={formData.email}
            onChangeText={(text) => setFormData({ ...formData, email: text })}
            style={styles.input}
            mode="outlined"
            keyboardType="email-address"
          />

          <TextInput
            label="Specialization"
            value={formData.specialization}
            onChangeText={(text) => setFormData({ ...formData, specialization: text })}
            style={styles.input}
            mode="outlined"
          />

          <TextInput
            label="Phone Number"
            value={formData.phoneNumber}
            onChangeText={(text) => setFormData({ ...formData, phoneNumber: text })}
            style={styles.input}
            mode="outlined"
          />

          <TextInput
            label="Designation"
            value={formData.designation}
            onChangeText={(text) => setFormData({ ...formData, designation: text })}
            style={styles.input}
            mode="outlined"
          />

          <TextInput
            label="Department"
            value={formData.department}
            onChangeText={(text) => setFormData({ ...formData, department: text })}
            style={styles.input}
            mode="outlined"
          />

          <TextInput
            label={currentEditingStaff ? "New Password (Optional)" : "Password"}
            value={formData.password}
            onChangeText={(text) => setFormData({ ...formData, password: text })}
            style={styles.input}
            mode="outlined"
            secureTextEntry={!passwordVisible}
            placeholder={currentEditingStaff ? "Leave blank to keep current password" : ""}
            right={<TextInput.Icon 
              icon={passwordVisible ? "eye" : "eye-off"} 
              onPress={() => setPasswordVisible(!passwordVisible)} 
            />}
          />

          <View style={styles.modalButtons}>
            <Button
              mode="outlined"
              onPress={() => { setShowModal(false); resetForm(); }}
              style={styles.modalButton}
            >
              Cancel
            </Button>
            {isLoading ? (
              <ActivityIndicator style={styles.modalButton} color={theme.colors.primary} />
            ) : (
              <Button
                mode="contained"
                onPress={handleAddOrEditStaff}
                style={styles.modalButton}
                buttonColor={theme.colors.primary}
              >
                {currentEditingStaff ? 'Update' : 'Add'}
              </Button>
            )}
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...globalStyles.container,
    paddingHorizontal: Platform.OS === 'web' ? theme.spacing.lg : theme.spacing.md,
  },
  // Header styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    flexWrap: 'nowrap',
  },
  headerSmallScreen: {
    flexDirection: 'column',
    alignItems: 'stretch',
  },
  headerLeftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  // Search bar styles
  searchBar: {
    marginBottom: theme.spacing.md,
    backgroundColor: theme.colors.surface,
  },
  // Staff list styles
  staffGridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  // Delete confirmation dialog styles
  deleteDialogContainer: {
    borderRadius: theme.roundness * 2,
    backgroundColor: theme.colors.surface,
    ...globalStyles.shadow,
  },
  iconContainer: {
    alignItems: 'center',
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  dialogTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  dialogContent: {
    paddingHorizontal: theme.spacing.sm,
    paddingBottom: theme.spacing.sm,
  },
  dialogMessage: {
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  dialogActions: {
    marginTop: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    paddingBottom: theme.spacing.sm,
    justifyContent: 'space-between',
  },
  cancelButton: {
    marginRight: theme.spacing.sm,
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
  },
  staffList: { flex: 1 },
  staffCard: {
    ...globalStyles.card,
    marginVertical: theme.spacing.sm,
  },
  staffCardGrid: {
    width: '48%',
    marginHorizontal: '1%',
  },
  staffHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  staffHeaderSmallScreen: {
    flexDirection: 'column',
  },
  staffName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  staffEmail: {
    fontSize: 14,
    color: theme.colors.placeholder,
    marginBottom: theme.spacing.xs,
  },
  divider: {
    marginVertical: theme.spacing.sm
  },
  staffDetailsContainer: {
    flexDirection: 'column',
  },
  staffDetails: {
    flexDirection: 'row',
    marginVertical: theme.spacing.xs,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.placeholder,
    width: 120,
  },
  detailValue: {
    fontSize: 14,
    color: theme.colors.text,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: theme.spacing.sm,
  },
  actionButton: {
    marginLeft: theme.spacing.sm,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    margin: 20,
    borderRadius: 10,
    alignSelf: 'center',
    maxWidth: 800,
  },
  modalTitle: { fontSize: 20, fontWeight: 'bold', marginBottom: theme.spacing.md },
  input: { marginBottom: theme.spacing.sm },
  modalButtons: { flexDirection: 'row', justifyContent: 'flex-end', marginTop: theme.spacing.md },
  modalButton: { marginLeft: theme.spacing.sm },
  error: { color: theme.colors.error, marginBottom: theme.spacing.sm },
});
