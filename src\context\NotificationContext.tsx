import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import { useAuth } from './AuthContext';
import { 
  registerForPushNotificationsAsync, 
  savePushToken, 
  removePushToken,
  handleNotificationResponse,
  NotificationChannels 
} from '../utils/notificationHelper';
import { useNavigation } from '@react-navigation/native';

interface NotificationContextType {
  expoPushToken: string | null;
  notification: Notifications.Notification | null;
  isNotificationPermissionGranted: boolean;
  registerForNotifications: () => Promise<void>;
  unregisterFromNotifications: () => Promise<void>;
  clearNotification: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const [notification, setNotification] = useState<Notifications.Notification | null>(null);
  const [isNotificationPermissionGranted, setIsNotificationPermissionGranted] = useState(false);
  
  const { user, token } = useAuth();
  const navigation = useNavigation();
  
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  // Register for push notifications
  const registerForNotifications = async () => {
    try {
      const pushToken = await registerForPushNotificationsAsync();
      
      if (pushToken && user?.id) {
        setExpoPushToken(pushToken);
        setIsNotificationPermissionGranted(true);
        
        // Save token to server
        const success = await savePushToken(user.id, pushToken, token || undefined);
        if (success) {
          console.log('Push token saved successfully');
        }
      }
    } catch (error) {
      console.error('Error registering for notifications:', error);
    }
  };

  // Unregister from push notifications
  const unregisterFromNotifications = async () => {
    try {
      if (user?.id) {
        await removePushToken(user.id, token || undefined);
      }
      setExpoPushToken(null);
      setIsNotificationPermissionGranted(false);
    } catch (error) {
      console.error('Error unregistering from notifications:', error);
    }
  };

  // Clear current notification
  const clearNotification = () => {
    setNotification(null);
  };

  // Setup notification listeners
  useEffect(() => {
    // Listen for notifications received while app is running
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      setNotification(notification);
    });

    // Listen for notification responses (when user taps notification)
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      const navigationData = handleNotificationResponse(response);
      
      if (navigationData && navigation) {
        // Navigate to the appropriate screen
        (navigation as any).navigate(navigationData.screen, navigationData.params);
      }
    });

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, [navigation]);

  // Auto-register when user logs in
  useEffect(() => {
    if (user && token && !expoPushToken) {
      registerForNotifications();
    }
  }, [user, token]);

  // Auto-unregister when user logs out
  useEffect(() => {
    if (!user && expoPushToken) {
      unregisterFromNotifications();
    }
  }, [user]);

  const value: NotificationContextType = {
    expoPushToken,
    notification,
    isNotificationPermissionGranted,
    registerForNotifications,
    unregisterFromNotifications,
    clearNotification,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
