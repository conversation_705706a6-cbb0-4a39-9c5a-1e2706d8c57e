import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import axios from 'axios';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// Configure how notifications appear when app is in foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Register for push notifications
export async function registerForPushNotificationsAsync(): Promise<string | undefined> {
  let token: string | undefined;
  
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#00A77E',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return undefined;
    }
    
    try {
      // Get project ID safely
      const projectId = Constants.expoConfig?.extra?.eas?.projectId;
      
      if (!projectId) {
        console.warn('No project ID found in Constants. Using default Expo push service.');
      }
      
      const expoPushToken = await Notifications.getExpoPushTokenAsync({
        projectId: projectId,
      });
      
      token = expoPushToken.data;
    } catch (error) {
      console.error('Error getting push token:', error);
    }
  } else {
    console.log('Must use physical device for Push Notifications');
  }

  return token;
}

// Save push token to server
export async function savePushToken(userId: string, token: string): Promise<boolean> {
  if (!userId || !token) {
    console.error('Invalid userId or token provided to savePushToken');
    return false;
  }
  
  try {
    if (!API_BASE_URL) {
      console.error('API_BASE_URL is not defined');
      return false;
    }
    
    await axios.post(`${API_BASE_URL}/api/notifications/token`, { userId, token });
    return true;
  } catch (error) {
    console.error('Error saving push token:', error);
    return false;
  }
}


