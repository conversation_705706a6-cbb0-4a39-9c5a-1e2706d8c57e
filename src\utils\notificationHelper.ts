import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// Configure how notifications appear when app is in foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Notification channels for different types
export const NotificationChannels = {
  CHAT: 'chat',
  DRUG_REACTION: 'drug-reaction',
  SYSTEM: 'system',
  EMERGENCY: 'emergency',
} as const;

// Setup notification channels for Android
async function setupNotificationChannels() {
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync(NotificationChannels.CHAT, {
      name: 'Chat Messages',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#00A77E',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync(NotificationChannels.DRUG_REACTION, {
      name: 'Drug Reactions',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 500, 250, 500],
      lightColor: '#FF6B6B',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync(NotificationChannels.SYSTEM, {
      name: 'System Notifications',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250],
      lightColor: '#4ECDC4',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync(NotificationChannels.EMERGENCY, {
      name: 'Emergency Alerts',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 1000, 500, 1000],
      lightColor: '#FF0000',
      sound: 'default',
    });
  }
}

// Register for push notifications
export async function registerForPushNotificationsAsync(): Promise<string | undefined> {
  let token: string | undefined;

  // Setup notification channels first
  await setupNotificationChannels();

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return undefined;
    }

    try {
      // Get project ID safely
      const projectId = Constants.expoConfig?.extra?.eas?.projectId;

      if (!projectId) {
        console.warn('No project ID found in Constants. Using default Expo push service.');
      }

      const expoPushToken = await Notifications.getExpoPushTokenAsync({
        projectId: projectId,
      });

      token = expoPushToken.data;

      // Store token locally for offline access
      if (token) {
        await AsyncStorage.setItem('expoPushToken', token);
      }
    } catch (error) {
      console.error('Error getting push token:', error);
    }
  } else {
    console.log('Must use physical device for Push Notifications');
  }

  return token;
}

// Save push token to server
export async function savePushToken(userId: string, token: string, authToken?: string): Promise<boolean> {
  if (!userId || !token) {
    console.error('Invalid userId or token provided to savePushToken');
    return false;
  }

  try {
    if (!API_BASE_URL) {
      console.error('API_BASE_URL is not defined');
      return false;
    }

    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (authToken) {
      headers.Authorization = `Bearer ${authToken}`;
    }

    await axios.post(`${API_BASE_URL}/api/notifications/token`,
      { userId, token },
      { headers }
    );
    return true;
  } catch (error) {
    console.error('Error saving push token:', error);
    return false;
  }
}

// Get stored push token
export async function getStoredPushToken(): Promise<string | null> {
  try {
    return await AsyncStorage.getItem('expoPushToken');
  } catch (error) {
    console.error('Error getting stored push token:', error);
    return null;
  }
}

// Remove push token from server (for logout)
export async function removePushToken(userId: string, authToken?: string): Promise<boolean> {
  try {
    if (!API_BASE_URL) {
      console.error('API_BASE_URL is not defined');
      return false;
    }

    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (authToken) {
      headers.Authorization = `Bearer ${authToken}`;
    }

    await axios.delete(`${API_BASE_URL}/api/notifications/token/${userId}`, { headers });
    await AsyncStorage.removeItem('expoPushToken');
    return true;
  } catch (error) {
    console.error('Error removing push token:', error);
    return false;
  }
}

// Handle notification response (when user taps notification)
export function handleNotificationResponse(response: Notifications.NotificationResponse) {
  const data = response.notification.request.content.data;

  if (data?.type === 'chat' && data?.chatId) {
    // Navigate to chat screen
    return {
      screen: 'CounsellorChat',
      params: { chatId: data.chatId, senderId: data.senderId }
    };
  } else if (data?.type === 'drug-reaction' && data?.reactionId) {
    // Navigate to drug reaction details
    return {
      screen: 'DrugReactionDetails',
      params: { reactionId: data.reactionId }
    };
  } else if (data?.type === 'system') {
    // Navigate to notifications or dashboard
    return {
      screen: 'Dashboard',
      params: {}
    };
  }

  return null;
}

// Schedule local notification (for testing or offline scenarios)
export async function scheduleLocalNotification(
  title: string,
  body: string,
  data?: any,
  channelId: string = NotificationChannels.SYSTEM
) {
  try {
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: 'default',
      },
      trigger: null, // Show immediately
      identifier: `local-${Date.now()}`,
    });
  } catch (error) {
    console.error('Error scheduling local notification:', error);
  }
}


