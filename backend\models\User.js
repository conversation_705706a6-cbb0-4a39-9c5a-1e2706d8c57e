const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  hospitalNo: {
    type: String,
    required: function() { return this.role === 'patient'; },
    trim: true,
  },
  firstName: {
    type: String,
    required: function() { return this.role === 'patient'; },
    trim: true,
  },
  lastName: {
    type: String,
    required: function() { return this.role === 'patient'; },
    trim: true,
  },
  age: {
    type: Number,
    required: function() { return this.role === 'patient'; },
  },
  sex: {
    type: String,
    enum: ['Male', 'Female'],
    required: function() { return this.role === 'patient'; },
  },
  weight: {
    type: Number,
    required: function() { return this.role === 'patient'; },
  },
  contactNumber: {
    type: String,
    required: function() { return this.role === 'patient'; },
    trim: true,
  },
  ethnicity: {
    type: String,
    enum: ['<PERSON><PERSON><PERSON>/<PERSON>', '<PERSON><PERSON>/Madhesi', 'Dalits', '<PERSON>ar', '<PERSON><PERSON><PERSON>', 'Muslim', 'Other'],
    required: function() { return this.role === 'patient'; },
    trim: true,
  },
  pregnant: {
    type: String,
    enum: ['Yes', 'No', 'Not Applicable'],
    required: function() { return this.role === 'patient'; },
    default: 'Not Applicable',
  },
  dateOfBirth: {
    type: Date,
    required: function() { return this.role === 'patient'; },
  },
  priorCounsel: {
    type: String,
    required: function() { return this.role === 'patient'; },
    enum: ['Yes', 'No'],
  },
  username: {
    type: String,
    unique: true,
    trim: true,
  },
  role: {
    type: String,
    enum: ['admin', 'doctor', 'drug-counsellor', 'patient'],
    required: true,
    default: 'patient',
  },
  phoneNumber: {
    type: String,
    required: function() { return this.role === 'doctor' || this.role === 'drug-counsellor'; },
    trim: true,
  },
  designation: {
    type: String,
    required: function() { return this.role === 'doctor' || this.role === 'drug-counsellor'; },
    trim: true,
  },
  department: {
    type: String,
    required: function() { return this.role === 'doctor' || this.role === 'drug-counsellor'; },
    trim: true,
  },
  specialization: {
    type: String,
    required: function() { return this.role === 'doctor' || this.role === 'drug-counsellor'; },
    trim: true,
  },
  // Push notification fields
  expoPushToken: {
    type: String,
    trim: true,
  },
  lastTokenUpdate: {
    type: Date,
  },
}, {
  timestamps: true,
});

const User = mongoose.model('User', userSchema);

module.exports = User;