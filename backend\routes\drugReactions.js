const express = require('express');
const router = express.Router();
const DrugReaction = require('../models/DrugReaction');
const User = require('../models/User');
const sendEmail = require('../utils/mailer'); // Assuming this utility handles email sending
const { sendPushNotifications } = require('../utils/pushNotifications');

router.get('/stats/total', async (req, res) => {
    try {
        const allReactions = await DrugReaction.find();

        const stats = {
            total: allReactions.length,
            pending: allReactions.filter(r => r.status === 'pending').length,
            resolved: allReactions.filter(r => r.status === 'resolved').length,
            bySeverity: {
                mild: allReactions.filter(r => r.severity === 'mild').length,
                moderate: allReactions.filter(r => r.severity === 'moderate').length,
                severe: allReactions.filter(r => r.severity === 'severe').length,
            }
        };

        res.status(200).json(stats);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error fetching total drug reaction statistics', error: error.message });
    }
});

router.get('/user/:userId/stats', async (req, res) => {
    const { userId } = req.params;

    try {
        const reactions = await DrugReaction.find({ user: userId })
            .sort({ dateReported: -1 });

        const stats = {
            total: reactions.length,
            pending: reactions.filter(r => r.status === 'pending').length,
            resolved: reactions.filter(r => r.status === 'resolved').length,
            bySeverity: {
                mild: reactions.filter(r => r.severity === 'mild').length,
                moderate: reactions.filter(r => r.severity === 'moderate').length,
                severe: reactions.filter(r => r.severity === 'severe').length,
            },
            recentReport: reactions.length > 0 ? reactions[0] : null
        };

        res.status(200).json(stats);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error fetching drug reaction statistics', error: error.message });
    }
});

router.get('/user/:userId', async (req, res) => {
    const { userId } = req.params;

    try {
        const reactions = await DrugReaction.find({ user: userId })
            .sort({ dateReported: -1 });
        res.status(200).json(reactions);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error fetching drug reactions', error: error.message });
    }
});

router.get('/', async (req, res) => {
    try {
        const reactions = await DrugReaction.find().populate('user', 'firstName lastName age sex contactNumber')
            .sort({ dateReported: -1 });
        res.status(200).json(reactions);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error fetching drug reactions', error: error.message });
    }
});

router.post('/', async (req, res) => {
    const { user, drugName, reactionDetails, symptoms, severity, dosageForm, routeOfAdministration, dailyDose, dateStarted, dateStopped, indications, image, outcomes, outcomesOthers } = req.body;

    try {
        const newReaction = new DrugReaction({
            user,
            drugName,
            reactionDetails,
            symptoms,
            severity,
            dosageForm,
            routeOfAdministration,
            dailyDose,
            dateStarted,
            dateStopped,
            indications,
            outcomes,
            outcomesOthers,
            image,
            status: 'pending',
            dateReported: new Date(),
            updatedAt: new Date()
        });

        await newReaction.save();
        // Fetch the patient who reported the reaction
        const patient = await User.findById(user);
        if (!patient) {
            return res.status(404).json({ message: 'Patient not found' });
        }

        // Fetch counsellors
        const counsellors = await User.find({ role: 'drug-counsellor' });
        const counsellorIds = counsellors.map(c => c._id);

        // Send push notifications to all counsellors
        await sendPushNotifications(
            counsellorIds,
            'New Drug Reaction Report',
            `${patient.name} has reported a reaction to ${drugName}`,
            { 
                type: 'reaction',
                reactionId: newReaction._id.toString(),
                patientId: patient._id.toString()
            }
        );

        // Send email notification to each counsellor
        counsellors.forEach(async (counsellor) => {
            if (counsellor.email) {
                const htmlContent = `
                    <h2 style="color:#333;">New Drug Reaction Report</h2>
                    <p><strong>Reported by:</strong> ${patient.name}</p>
                    <table style="border-collapse:collapse; width:100%; font-family:sans-serif;">
                      <thead>
                        <tr style="background-color:#0FAC86;">
                          <th style="border:1px solid #ccc; padding:8px; color:#fff;">Label</th>
                          <th style="border:1px solid #ccc; padding:8px; color:#fff;">Description</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td style="border:1px solid #ccc; padding:8px;">Drug Name</td>
                          <td style="border:1px solid #ccc; padding:8px;">${drugName}</td>
                        </tr>
                        <tr>
                          <td style="border:1px solid #ccc; padding:8px;">Severity</td>
                          <td style="border:1px solid #ccc; padding:8px;">${severity}</td>
                        </tr>
                        <tr>
                          <td style="border:1px solid #ccc; padding:8px;">Symptoms</td>
                          <td style="border:1px solid #ccc; padding:8px;">${symptoms}</td>
                        </tr>
                        <tr>
                          <td style="border:1px solid #ccc; padding:8px;">Reaction Details</td>
                          <td style="border:1px solid #ccc; padding:8px;">${reactionDetails}</td>
                        </tr>
                        <tr>
                          <td style="border:1px solid #ccc; padding:8px;">Date Reported</td>
                          <td style="border:1px solid #ccc; padding:8px;">${new Date().toLocaleDateString()}</td>
                        </tr>
                      </tbody>
                    </table>
                    <p>Please log in to the ADR system to review this report.</p>
                `;

                await sendEmail({
                    to: counsellor.email,
                    subject: `New Drug Reaction Report from ${patient.name}`,
                    text: `A new drug reaction was submitted by ${patient.name}. Please review it in the ADR system.`,
                    html: htmlContent
                });
            }
        });

        res.status(201).json({ message: 'Drug reaction reported successfully', data: newReaction });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error reporting drug reaction', error: error.message });
    }
});

router.get('/:id', async (req, res) => {
    try {
        const reaction = await DrugReaction.findById(req.params.id);
        if (!reaction) {
            return res.status(404).json({ message: 'Reaction not found' });
        }
        res.status(200).json(reaction);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error fetching drug reaction', error: error.message });
    }
});

router.put('/:id', async (req, res) => {
    try {
        const updatedReaction = await DrugReaction.findByIdAndUpdate(
            req.params.id,
            {
                ...req.body,
                updatedAt: new Date()
            },
            { new: true }
        );
        if (!updatedReaction) {
            return res.status(404).json({ message: 'Reaction not found' });
        }
        res.status(200).json(updatedReaction);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error updating drug reaction', error: error.message });
    }
});

// Get allergy card statistics
router.get('/stats/allergy-cards', async (req, res) => {
    try {
        const allReactions = await DrugReaction.find();

        // Count reactions with and without allergy cards
        const allergyCardStats = {
            withAllergyCard: allReactions.filter(r => r.allergyCard === true).length,
            withoutAllergyCard: allReactions.filter(r => r.allergyCard === false || r.allergyCard === undefined).length
        };

        res.status(200).json(allergyCardStats);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error fetching allergy card statistics', error: error.message });
    }
});

// Get frequency of reported drug reactions
router.get('/stats/reaction-frequency', async (req, res) => {
    try {
        const allReactions = await DrugReaction.find();

        // Create a map to count occurrences of each reaction type
        const reactionFrequency = {};

        // Count by drug name
        allReactions.forEach(reaction => {
            const drugName = reaction.drugName;
            if (drugName) {
                if (reactionFrequency[drugName]) {
                    reactionFrequency[drugName]++;
                } else {
                    reactionFrequency[drugName] = 1;
                }
            }
        });

        // Convert to array of objects for easier sorting and processing in frontend
        const frequencyData = Object.keys(reactionFrequency).map(name => ({
            name,
            count: reactionFrequency[name]
        }));

        // Sort by frequency in descending order
        frequencyData.sort((a, b) => b.count - a.count);

        res.status(200).json(frequencyData);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Error fetching drug reaction frequency statistics', error: error.message });
    }
});

module.exports = router;
