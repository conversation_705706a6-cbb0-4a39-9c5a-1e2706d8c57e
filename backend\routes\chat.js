const express = require('express');
const Message = require('../models/Message');
const mongoose = require('mongoose');
const router = express.Router();
const sendEmail = require('../utils/mailer');
const User = require('../models/User');
const { sendPushNotifications } = require('../utils/pushNotifications');

router.get('/history/:senderId/:receiverId', async (req, res) => {
  try {
    const { senderId, receiverId } = req.params;

    console.log(`Fetching chat history for sender: ${senderId}, receiver: ${receiverId}`);

    if (!mongoose.Types.ObjectId.isValid(senderId) || !mongoose.Types.ObjectId.isValid(receiverId)) {
      console.error('Invalid sender or receiver ID');
      return res.status(400).json({ error: 'Invalid user IDs' });
    }

    // Retrieve messages in descending order (latest first)
    const messages = await Message.find({
      $or: [
        { senderId: senderId, receiverId: receiverId },
        { senderId: receiverId, receiverId: senderId }
      ]
    })
    .sort({ timestamp: -1 }) // Sorting newest to oldest
    .populate('senderId', 'name')
    .populate('receiverId', 'name');

    if (messages.length === 0) {
      console.warn(`No messages found for sender ${senderId} and receiver ${receiverId}`);
    }

    console.log(`Messages retrieved: ${messages.length}`);
    res.json(messages);
  } catch (error) {
    console.error('Error fetching chat history:', error);
    res.status(500).json({ error: 'Failed to retrieve messages' });
  }
});


/**
 * Send a new chat message
 */
router.post('/send', async (req, res) => {
  try {
    const { sender, receiver, content, attachment } = req.body;

    if (!mongoose.Types.ObjectId.isValid(sender) || !mongoose.Types.ObjectId.isValid(receiver)) {
      return res.status(400).json({ error: 'Invalid user IDs' });
    }

    if (!content && !attachment) {
      return res.status(400).json({ error: 'Message must have content or attachment' });
    }

    // Validate attachment if present
    if (attachment) {
      // Check file size (limit to 10MB)
      const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
      if (attachment.fileData && Buffer.from(attachment.fileData, 'base64').length > MAX_FILE_SIZE) {
        return res.status(400).json({ error: 'File size exceeds the 10MB limit' });
      }

      // Validate file type
      const allowedFileTypes = ['image', 'document', 'audio', 'video', 'other'];
      if (!allowedFileTypes.includes(attachment.fileType)) {
        return res.status(400).json({ error: 'Invalid file type' });
      }

      // Validate mime type for security
      const allowedMimeTypes = [
        // Images
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        // Documents
        'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        // Audio
        'audio/mpeg', 'audio/wav', 'audio/ogg',
        // Video
        'video/mp4', 'video/quicktime', 'video/webm'
      ];

      if (!allowedMimeTypes.includes(attachment.mimeType)) {
        return res.status(400).json({ error: 'Unsupported file format' });
      }
    }

    // Save message
    const message = new Message({
      senderId: sender,
      receiverId: receiver,
      content: content || '',
      attachment,
      timestamp: new Date(),
    });

    const savedMessage = await message.save();
    const populatedMessage = await Message.findById(savedMessage._id)
      .populate('senderId', 'name email role')
      .populate('receiverId', 'name email role');

    const io = req.app.get('io');
    const messagePayload = {
      id: savedMessage._id,
      text: content || '',
      attachment: attachment || null,
      timestamp: savedMessage.timestamp,
      senderId: sender,
      senderName: populatedMessage.senderId.name,
      receiverId: receiver,
    };

    // Send push notification to receiver
    const receiverUser = await User.findById(receiver);
    if (receiverUser) {
      const senderUser = await User.findById(sender);
      const senderName = senderUser ? senderUser.name : 'Unknown';
      
      await sendPushNotifications(
        [receiver],
        `New message from ${senderName}`,
        content.length > 50 ? `${content.substring(0, 47)}...` : content,
        {
          type: 'chat',
          chatId: messagePayload.id,
          senderId: sender
        }
      );
    }

    // Continue with socket emission
    io.to(receiver).emit('receive-message', messagePayload);
    io.to(sender).emit('receive-message', messagePayload);

    // 👉 Send email to counsellor
    // const senderUser = populatedMessage.senderId;
    // const receiverUser = populatedMessage.receiverId;

    //   if (receiverUser.email) {
    //     const subject = `New Message from ${senderUser.role === 'drug-counsellor' ? 'doctor' : 'Patient'}`;
    //     const text = `Dear ${receiverUser.name},\n\nYou have received a new message from ${senderUser.name}:\n\n"${content}"\n\nPlease log in to the ADR system to respond.\n\nRegards,\nADR Notification System`;

    //     await sendEmail({
    //       to: receiverUser.email,
    //       subject,
    //       text,
    //     });
    //   }

    res.status(201).json(populatedMessage);
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
});
// Mark messages as read
router.patch('/mark-as-read', async (req, res) => {
  try {
    const { senderId, receiverId } = req.body;

    if (!mongoose.Types.ObjectId.isValid(senderId) || !mongoose.Types.ObjectId.isValid(receiverId)) {
      return res.status(400).json({ error: 'Invalid user IDs' });
    }

    const result = await Message.updateMany(
      { senderId, receiverId, read: false },
      { $set: { read: true } }
    );

    res.status(200).json({ message: 'Messages marked as read', modifiedCount: result.modifiedCount });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({ error: 'Failed to mark messages as read' });
  }
});

module.exports = router;
