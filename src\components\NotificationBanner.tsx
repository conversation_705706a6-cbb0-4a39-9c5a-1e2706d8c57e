import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { Text, IconButton } from 'react-native-paper';
import { useNotification } from '../context/NotificationContext';
import { theme } from '../theme';

interface NotificationBannerProps {
  onPress?: () => void;
}

export const NotificationBanner: React.FC<NotificationBannerProps> = ({ onPress }) => {
  const { notification, clearNotification } = useNotification();
  const [slideAnim] = useState(new Animated.Value(-100));
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (notification) {
      setIsVisible(true);
      // Slide down animation
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Auto-hide after 5 seconds
      const timer = setTimeout(() => {
        handleDismiss();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [notification]);

  const handleDismiss = () => {
    // Slide up animation
    Animated.timing(slideAnim, {
      toValue: -100,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
      clearNotification();
    });
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    }
    handleDismiss();
  };

  if (!isVisible || !notification) {
    return null;
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'chat':
        return theme.colors.primary;
      case 'drug-reaction':
        return '#FF6B6B';
      case 'emergency':
        return '#FF0000';
      default:
        return theme.colors.secondary;
    }
  };

  const notificationType = notification.request.content.data?.type || 'system';
  const backgroundColor = getNotificationColor(notificationType);

  return (
    <Animated.View
      style={[
        styles.container,
        { backgroundColor, transform: [{ translateY: slideAnim }] }
      ]}
    >
      <TouchableOpacity style={styles.content} onPress={handlePress} activeOpacity={0.8}>
        <View style={styles.textContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {notification.request.content.title}
          </Text>
          <Text style={styles.body} numberOfLines={2}>
            {notification.request.content.body}
          </Text>
        </View>
        <IconButton
          icon="close"
          iconColor="#FFFFFF"
          size={20}
          onPress={handleDismiss}
          style={styles.closeButton}
        />
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingTop: 50, // Account for status bar
    paddingHorizontal: 16,
    paddingBottom: 16,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textContainer: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  body: {
    color: '#FFFFFF',
    fontSize: 14,
    opacity: 0.9,
  },
  closeButton: {
    margin: 0,
  },
});
