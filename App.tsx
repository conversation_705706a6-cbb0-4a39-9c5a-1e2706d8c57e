import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider as PaperProvider } from 'react-native-paper';
import { Navigation } from './src/navigation';
import { AuthProvider } from './src/context/AuthContext';
import { DrugReactionProvider } from './src/context/DrugReactionContext';
import { PatientProvider } from './src/context/PatientContext';
import { DrugCounsellingProvider } from './src/context/DrugCounsellingContext';
import { NotificationProvider } from './src/context/NotificationContext';
import { theme } from './src/theme';
import { LogBox } from 'react-native';

// Ignore specific warnings that might be causing issues
LogBox.ignoreLogs([
  'Warning: ...',  // Add specific warnings here if needed
]);

export default function App() {
  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AuthProvider>
          <NotificationProvider>
            <PatientProvider>
              <DrugReactionProvider>
                <DrugCounsellingProvider> {/* Wrap Navigation with DrugCounsellingProvider */}
                  <Navigation />
                </DrugCounsellingProvider>
              </DrugReactionProvider>
            </PatientProvider>
          </NotificationProvider>
        </AuthProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
}
