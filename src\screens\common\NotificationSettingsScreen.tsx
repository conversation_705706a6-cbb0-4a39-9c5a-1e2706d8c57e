import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Appbar } from 'react-native-paper';
import { NotificationSettings } from '../../components/NotificationSettings';
import { theme } from '../../theme';
import { useNavigation } from '@react-navigation/native';

export const NotificationSettingsScreen: React.FC = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Notification Settings" />
      </Appbar.Header>
      
      <ScrollView style={styles.content}>
        <NotificationSettings />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    backgroundColor: theme.colors.primary,
  },
  content: {
    flex: 1,
  },
});
