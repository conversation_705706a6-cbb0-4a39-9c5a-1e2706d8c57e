const express = require('express');
const { Expo } = require('expo-server-sdk');
const User = require('../models/User');
const auth = require('../middleware/auth');

const router = express.Router();

// Create a new Expo SDK client
const expo = new Expo();

// Store push tokens in memory (in production, use a database)
const pushTokens = new Map();

// Save push token
router.post('/token', auth, async (req, res) => {
  try {
    const { userId, token } = req.body;
    
    if (!userId || !token) {
      return res.status(400).json({ error: 'userId and token are required' });
    }

    // Validate the token format
    if (!Expo.isExpoPushToken(token)) {
      return res.status(400).json({ error: 'Invalid Expo push token format' });
    }

    // Store the token (in production, save to database)
    pushTokens.set(userId, token);
    
    // Optionally, save to user document in database
    await User.findByIdAndUpdate(userId, { 
      expoPushToken: token,
      lastTokenUpdate: new Date()
    });

    console.log(`Push token saved for user ${userId}: ${token}`);
    res.status(200).json({ message: 'Push token saved successfully' });
  } catch (error) {
    console.error('Error saving push token:', error);
    res.status(500).json({ error: 'Failed to save push token' });
  }
});

// Remove push token
router.delete('/token/:userId', auth, async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Remove from memory
    pushTokens.delete(userId);
    
    // Remove from database
    await User.findByIdAndUpdate(userId, { 
      $unset: { expoPushToken: 1, lastTokenUpdate: 1 }
    });

    console.log(`Push token removed for user ${userId}`);
    res.status(200).json({ message: 'Push token removed successfully' });
  } catch (error) {
    console.error('Error removing push token:', error);
    res.status(500).json({ error: 'Failed to remove push token' });
  }
});

// Get user's push token
router.get('/token/:userId', auth, async (req, res) => {
  try {
    const { userId } = req.params;
    
    // First check memory
    let token = pushTokens.get(userId);
    
    // If not in memory, check database
    if (!token) {
      const user = await User.findById(userId);
      token = user?.expoPushToken;
      
      // Store in memory for faster access
      if (token) {
        pushTokens.set(userId, token);
      }
    }

    res.status(200).json({ token: token || null });
  } catch (error) {
    console.error('Error getting push token:', error);
    res.status(500).json({ error: 'Failed to get push token' });
  }
});

// Send push notification to specific users
async function sendPushNotifications(userIds, title, body, data = {}) {
  try {
    if (!Array.isArray(userIds)) {
      userIds = [userIds];
    }

    const messages = [];

    for (const userId of userIds) {
      // Get token from memory first, then database
      let pushToken = pushTokens.get(userId);
      
      if (!pushToken) {
        const user = await User.findById(userId);
        pushToken = user?.expoPushToken;
        
        if (pushToken) {
          pushTokens.set(userId, pushToken);
        }
      }

      if (pushToken && Expo.isExpoPushToken(pushToken)) {
        messages.push({
          to: pushToken,
          sound: 'default',
          title,
          body,
          data,
          channelId: data.type || 'default',
        });
      } else {
        console.log(`No valid push token found for user ${userId}`);
      }
    }

    if (messages.length === 0) {
      console.log('No valid push tokens found for the specified users');
      return { success: false, error: 'No valid push tokens' };
    }

    // Send notifications in chunks
    const chunks = expo.chunkPushNotifications(messages);
    const tickets = [];

    for (const chunk of chunks) {
      try {
        const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
        tickets.push(...ticketChunk);
      } catch (error) {
        console.error('Error sending push notification chunk:', error);
      }
    }

    console.log(`Sent ${messages.length} push notifications`);
    return { success: true, tickets };
  } catch (error) {
    console.error('Error in sendPushNotifications:', error);
    return { success: false, error: error.message };
  }
}

// Test notification endpoint
router.post('/test', auth, async (req, res) => {
  try {
    const userId = req.user._id.toString();
    const { title = 'Test Notification', body = 'This is a test notification from your Drug Counselling App!' } = req.body;

    const result = await sendPushNotifications(
      [userId],
      title,
      body,
      { type: 'system', test: true }
    );

    if (result.success) {
      res.status(200).json({ message: 'Test notification sent successfully', tickets: result.tickets });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Error sending test notification:', error);
    res.status(500).json({ error: 'Failed to send test notification' });
  }
});

// Send notification to all users with specific role
router.post('/broadcast', auth, async (req, res) => {
  try {
    const { role, title, body, data = {} } = req.body;
    
    if (!role || !title || !body) {
      return res.status(400).json({ error: 'role, title, and body are required' });
    }

    // Find all users with the specified role
    const users = await User.find({ role }).select('_id');
    const userIds = users.map(user => user._id.toString());

    if (userIds.length === 0) {
      return res.status(404).json({ error: `No users found with role: ${role}` });
    }

    const result = await sendPushNotifications(userIds, title, body, data);

    if (result.success) {
      res.status(200).json({ 
        message: `Broadcast sent to ${userIds.length} users with role: ${role}`,
        tickets: result.tickets 
      });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Error broadcasting notification:', error);
    res.status(500).json({ error: 'Failed to broadcast notification' });
  }
});

module.exports = { router, sendPushNotifications };
