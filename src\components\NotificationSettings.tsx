import React from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Card, Text, Switch, Button, Divider } from 'react-native-paper';
import { useNotification } from '../context/NotificationContext';
import { theme } from '../theme';

interface NotificationSettingsProps {
  onClose?: () => void;
}

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({ onClose }) => {
  const { 
    isNotificationPermissionGranted, 
    registerForNotifications, 
    unregisterFromNotifications,
    expoPushToken 
  } = useNotification();

  const handleToggleNotifications = async () => {
    try {
      if (isNotificationPermissionGranted) {
        Alert.alert(
          'Disable Notifications',
          'Are you sure you want to disable push notifications? You will no longer receive alerts for new messages and drug reactions.',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Disable', 
              style: 'destructive',
              onPress: async () => {
                await unregisterFromNotifications();
                Alert.alert('Success', 'Notifications have been disabled.');
              }
            }
          ]
        );
      } else {
        await registerForNotifications();
        if (expoPushToken) {
          Alert.alert('Success', 'Notifications have been enabled successfully!');
        } else {
          Alert.alert(
            'Permission Required', 
            'Please allow notifications in your device settings to receive push notifications.'
          );
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update notification settings. Please try again.');
    }
  };

  const handleTestNotification = () => {
    Alert.alert(
      'Test Notification',
      'This feature will be available once the backend is fully configured. For now, you can test notifications by sending a message in the chat.'
    );
  };

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.title}>Notification Settings</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Push Notifications</Text>
              <Text style={styles.settingDescription}>
                Receive notifications for new messages, drug reactions, and system alerts
              </Text>
            </View>
            <Switch
              value={isNotificationPermissionGranted}
              onValueChange={handleToggleNotifications}
              color={theme.colors.primary}
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.statusContainer}>
            <Text style={styles.statusTitle}>Status</Text>
            <Text style={[
              styles.statusText,
              { color: isNotificationPermissionGranted ? theme.colors.primary : theme.colors.error }
            ]}>
              {isNotificationPermissionGranted ? 'Enabled' : 'Disabled'}
            </Text>
          </View>

          {expoPushToken && (
            <View style={styles.tokenContainer}>
              <Text style={styles.tokenTitle}>Device Token</Text>
              <Text style={styles.tokenText} numberOfLines={2}>
                {expoPushToken.substring(0, 50)}...
              </Text>
            </View>
          )}

          <View style={styles.buttonContainer}>
            <Button
              mode="outlined"
              onPress={handleTestNotification}
              style={styles.button}
              disabled={!isNotificationPermissionGranted}
            >
              Test Notification
            </Button>
            
            {onClose && (
              <Button
                mode="contained"
                onPress={onClose}
                style={styles.button}
              >
                Close
              </Button>
            )}
          </View>
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: theme.colors.background,
  },
  card: {
    elevation: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: theme.colors.primary,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  divider: {
    marginVertical: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  tokenContainer: {
    marginBottom: 16,
  },
  tokenTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  tokenText: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    fontFamily: 'monospace',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
});
